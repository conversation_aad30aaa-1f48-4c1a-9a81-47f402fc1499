25-08-11.10:16:56.034 [main            ] INFO  Application            - Starting Application using Java 17.0.14 with PID 50552 (E:\Java\ai-rag-knowledge-20602\clw-dev-tech-app\target\classes started by ChenSir in E:\Java\ai-rag-knowledge-20602)
25-08-11.10:16:56.036 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-11.10:16:56.561 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-08-11.10:16:56.564 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-08-11.10:16:56.590 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
25-08-11.10:16:56.958 [main            ] INFO  TomcatWebServer        - <PERSON><PERSON> initialized with port 8090 (http)
25-08-11.10:16:56.968 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8090"]
25-08-11.10:16:56.970 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-11.10:16:56.970 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-08-11.10:16:57.023 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-11.10:16:57.023 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 955 ms
25-08-11.10:16:57.520 [main            ] INFO  Version                - Redisson 3.44.0
25-08-11.10:16:57.822 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-11.10:16:57.844 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-11.10:16:58.442 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8090"]
25-08-11.10:16:58.451 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8090 (http) with context path ''
25-08-11.10:16:58.457 [main            ] INFO  Application            - Started Application in 2.89 seconds (process running for 4.065)
25-08-11.10:17:19.752 [main            ] INFO  Application            - Starting Application using Java 17.0.14 with PID 50384 (E:\Java\ai-rag-knowledge-20602\clw-dev-tech-app\target\classes started by ChenSir in E:\Java\ai-rag-knowledge-20602)
25-08-11.10:17:19.754 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-11.10:17:20.168 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-08-11.10:17:20.170 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-08-11.10:17:20.198 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
25-08-11.10:17:20.510 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8090 (http)
25-08-11.10:17:20.518 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8090"]
25-08-11.10:17:20.519 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-11.10:17:20.519 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.19]
25-08-11.10:17:20.558 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-11.10:17:20.559 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 777 ms
25-08-11.10:17:20.909 [main            ] INFO  Version                - Redisson 3.44.0
25-08-11.10:17:21.136 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-11.10:17:21.151 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-11.10:17:21.538 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8090"]
25-08-11.10:17:21.546 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8090 (http) with context path ''
25-08-11.10:17:21.552 [main            ] INFO  Application            - Started Application in 2.272 seconds (process running for 2.863)
25-08-11.10:17:34.176 [http-nio-8090-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-08-11.10:17:34.176 [http-nio-8090-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-08-11.10:17:34.178 [http-nio-8090-exec-1] INFO  DispatcherServlet      - Completed initialization in 2 ms
