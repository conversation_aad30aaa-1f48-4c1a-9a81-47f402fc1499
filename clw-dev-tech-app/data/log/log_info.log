25-08-12.22:00:09.921 [main            ] WARN  DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/org/json/json/20231013/json-20231013.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

25-08-12.22:00:09.936 [main            ] INFO  RAGTest                - Starting RAGTest using Java 17.0.14 with PID 66364 (started by ChenSir in E:\Java\ai-rag-knowledge-20602\clw-dev-tech-app)
25-08-12.22:00:09.937 [main            ] INFO  RAGTest                - The following 1 profile is active: "dev"
25-08-12.22:00:10.619 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-08-12.22:00:10.622 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-08-12.22:00:10.657 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
25-08-12.22:00:11.555 [main            ] INFO  HikariDataSource       - HikariCP - Starting...
25-08-12.22:00:11.717 [main            ] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@4ade94f4
25-08-12.22:00:11.719 [main            ] INFO  HikariDataSource       - HikariCP - Start completed.
25-08-12.22:00:13.514 [main            ] INFO  Version                - Redisson 3.44.0
25-08-12.22:00:13.826 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-12.22:00:13.846 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-12.22:00:14.660 [main            ] INFO  RAGTest                - Started RAGTest in 5.167 seconds (process running for 5.974)
25-08-12.22:00:16.128 [main            ] INFO  RAGTest                - 上传完成
25-08-12.22:00:16.150 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-08-12.22:00:16.153 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
25-08-12.22:00:35.334 [main            ] WARN  DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/org/json/json/20231013/json-20231013.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

25-08-12.22:00:35.345 [main            ] INFO  RAGTest                - Starting RAGTest using Java 17.0.14 with PID 54988 (started by ChenSir in E:\Java\ai-rag-knowledge-20602\clw-dev-tech-app)
25-08-12.22:00:35.347 [main            ] INFO  RAGTest                - The following 1 profile is active: "dev"
25-08-12.22:00:35.943 [main            ] INFO  RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
25-08-12.22:00:35.946 [main            ] INFO  RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
25-08-12.22:00:35.968 [main            ] INFO  RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
25-08-12.22:00:36.707 [main            ] INFO  HikariDataSource       - HikariCP - Starting...
25-08-12.22:00:36.818 [main            ] INFO  HikariPool             - HikariCP - Added connection org.postgresql.jdbc.PgConnection@5dd9bff
25-08-12.22:00:36.820 [main            ] INFO  HikariDataSource       - HikariCP - Start completed.
25-08-12.22:00:37.364 [main            ] INFO  Version                - Redisson 3.44.0
25-08-12.22:00:37.620 [redisson-netty-1-4] INFO  ConnectionsHolder      - 1 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-12.22:00:37.643 [redisson-netty-1-13] INFO  ConnectionsHolder      - 5 connections initialized for 127.0.0.1/127.0.0.1:16379
25-08-12.22:00:38.468 [main            ] INFO  RAGTest                - Started RAGTest in 3.593 seconds (process running for 4.387)
25-08-12.22:00:50.009 [main            ] INFO  RAGTest                - 测试结果:{"metadata":{"promptMetadata":{},"rateLimit":{"requestsLimit":0,"requestsRemaining":0,"requestsReset":{"nano":0,"negative":false,"seconds":0,"units":["SECONDS","NANOS"],"zero":true},"tokensLimit":0,"tokensRemaining":0,"tokensReset":{"$ref":"$.metadata.rateLimit.requestsReset"}},"usage":{"generationTokens":0,"promptTokens":0,"totalTokens":0}},"result":{"metadata":{"contentFilterMetadata":{"generationTokens":212,"promptTokens":76,"totalTokens":288},"finishReason":"unknown"},"output":{"content":"<think>\n嗯，用户给了一个关于“王大瓜”的问题，并提供了相关的信息。用户明确要求提供准确的答案，但用假设的方式来回答，并且必须用中文回复。同时，还提到如果不确定，就简单说明。\n\n首先，我需要分析提供的信息：“王大瓜 1990年出生”。这可能是一个名字和出生年份的组合。接下来，用户的查询是“王大瓜，哪年出生”，这直接询问了出生年份。所以，我应该基于提供的信息直接回答，不需要更多的推导。\n\n用户可能是在测试我的回复是否正确或是否需要解释，因为他们在用中文，或者他们是在练习特定格式。因此，我只需要按照示例中的方式来回应，提供出生年份，并用简短的中文表达出来。\n\n另外，用户强调了“如果不知道就简单说明”，但在这个情况下，信息明确，所以不需要复杂回答。直接给出出生年份即可。\n</think>\n\n王大瓜，哪年出生  \n1990","media":[],"messageType":"ASSISTANT","properties":{}}},"results":[{"$ref":"$.metadata.rateLimit.result"}]}
25-08-12.22:00:50.030 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown initiated...
25-08-12.22:00:50.032 [SpringApplicationShutdownHook] INFO  HikariDataSource       - HikariCP - Shutdown completed.
