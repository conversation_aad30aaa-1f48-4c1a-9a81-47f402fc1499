25-08-12.22:00:09.921 [main            ] WARN  DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/org/json/json/20231013/json-20231013.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

25-08-12.22:00:35.334 [main            ] WARN  DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer - 

Found multiple occurrences of org.json.JSONObject on the class path:

	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class
	jar:file:/E:/JavaTools/apache-maven-3.9.9/maven_repo/org/json/json/20231013/json-20231013.jar!/org/json/JSONObject.class

You may wish to exclude one of them to ensure predictable runtime behavior

