<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .app-title {
            font-size: 32px;
            color: #1877f2;
            margin-bottom: 10px;
        }

        .app-subtitle {
            color: #65676b;
            font-size: 16px;
        }

        .chat-area {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .message-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .message-card.user {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            color: white;
            margin-left: 20%;
        }

        .message-card.ai {
            background: white;
            margin-right: 20%;
            border-left: 4px solid #1877f2;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user .avatar {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .ai .avatar {
            background: #1877f2;
            color: white;
        }

        .message-info {
            flex: 1;
        }

        .sender-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
        }

        .message-text {
            line-height: 1.6;
            font-size: 15px;
        }

        .input-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            bottom: 20px;
        }

        .input-container {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 50px;
            padding: 15px 20px;
            border: 2px solid #e4e6ea;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #1877f2;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #1877f2;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #166fe5;
        }

        .send-button:disabled {
            background: #bcc0c4;
            cursor: not-allowed;
        }

        .status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: #e8f5e8;
            color: #2e7d32;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .model-select {
            padding: 5px 10px;
            border: 1px solid #4caf50;
            border-radius: 5px;
            background: white;
            color: #2e7d32;
            font-size: 13px;
            outline: none;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .status-dot.disconnected {
            background: #f44336;
        }

        .status-dot.connecting {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1 class="app-title">🧠 DeepSeek AI Chat</h1>
            <p class="app-subtitle">智能对话助手 - 流式响应体验</p>
        </div>

        <div class="status-bar">
            <div class="status-left">
                <div class="connection-status">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="connectionStatus">已连接</span>
                </div>
                <span>模式：流式对话</span>
            </div>
            <div class="status-right">
                <label for="modelSelect">模型：</label>
                <select class="model-select" id="modelSelect">
                    <option value="deepseek-r1:1.5b">DeepSeek R1 1.5B</option>
                    <option value="deepseek-r1:7b">DeepSeek R1 7B</option>
                    <option value="deepseek-r1:14b">DeepSeek R1 14B</option>
                    <option value="deepseek-r1:32b">DeepSeek R1 32B</option>
                </select>
            </div>
        </div>

        <div class="chat-area" id="chatArea">
            <div class="message-card ai">
                <div class="message-header">
                    <div class="avatar">🤖</div>
                    <div class="message-info">
                        <div class="sender-name">DeepSeek AI</div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
                <div class="message-text">
                    欢迎使用DeepSeek AI助手！我已准备好为您提供帮助。请随时向我提问，我会以流式方式实时回复您。
                </div>
            </div>
        </div>

        <div class="input-card">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea class="message-input" id="messageInput" placeholder="输入您的问题..." rows="1"></textarea>
                </div>
                <button class="send-button" id="sendButton">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const modelSelect = document.getElementById('modelSelect');
        const statusDot = document.getElementById('statusDot');
        const connectionStatus = document.getElementById('connectionStatus');

        let currentEventSource = null;
        let isGenerating = false;

        function addMessageCard(content, type, isStreaming = false) {
            const card = document.createElement('div');
            card.className = `message-card ${type}`;

            const avatar = type === 'user' ? '👤' : '🤖';
            const name = type === 'user' ? '您' : 'DeepSeek AI';
            const time = new Date().toLocaleTimeString();
            const messageId = `message-${Date.now()}`;

            card.innerHTML = `
                <div class="message-header">
                    <div class="avatar">${avatar}</div>
                    <div class="message-info">
                        <div class="sender-name">${name}</div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
                <div class="message-text" id="${messageId}">${content}</div>
            `;

            chatArea.appendChild(card);
            card.scrollIntoView({ behavior: 'smooth' });

            return card.querySelector('.message-text');
        }

        // 流式响应处理函数
        function streamResponse(message, aiMessageElement) {
            const model = modelSelect.value;
            const apiUrl = `http://localhost:8090/api/v1/ollama/generate_stream?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;

            updateConnectionStatus('connecting', '连接中...');

            currentEventSource = new EventSource(apiUrl);
            let fullContent = '';

            currentEventSource.onopen = function(event) {
                console.log('EventSource连接已建立');
                updateConnectionStatus('connected', '已连接');
                // 清除初始的"正在思考..."文本
                aiMessageElement.textContent = '';
            };

            currentEventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    // 处理数组格式的响应
                    if (Array.isArray(data) && data.length > 0) {
                        const result = data[0].result;
                        const content = result.output.content;
                        const finishReason = result.metadata.finishReason;

                        // 如果有内容，追加到当前消息（注意content可能为空）
                        if (content) {
                            fullContent += content;
                            aiMessageElement.textContent = fullContent;
                            // 滚动到最新消息
                            aiMessageElement.scrollIntoView({ behavior: 'smooth' });
                        }

                        // 检查是否结束
                        if (finishReason === 'STOP') {
                            finishResponse();
                        }
                    }
                } catch (error) {
                    console.error('解析响应数据失败:', error);
                    handleResponseError('数据解析失败: ' + error.message);
                }
            };

            currentEventSource.onerror = function(event) {
                console.error('EventSource连接错误:', event);
                handleResponseError('连接失败，请检查网络或服务状态');
            };
        }

        // 完成响应处理
        function finishResponse() {
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }

            isGenerating = false;
            sendButton.disabled = false;
            updateConnectionStatus('connected', '已连接');

            console.log('AI响应完成');
        }

        // 处理响应错误
        function handleResponseError(errorMessage) {
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }

            // 在聊天区域显示错误消息
            const errorCard = addMessageCard(`❌ 错误: ${errorMessage}`, 'ai');
            errorCard.style.color = '#f44336';

            isGenerating = false;
            sendButton.disabled = false;
            updateConnectionStatus('disconnected', '连接失败');

            console.error('响应错误:', errorMessage);
        }

        // 更新连接状态
        function updateConnectionStatus(status, text) {
            statusDot.className = `status-dot ${status}`;
            connectionStatus.textContent = text;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isGenerating) return;

            // 添加用户消息卡片
            addMessageCard(message, 'user');
            messageInput.value = '';

            // 设置生成状态
            isGenerating = true;
            sendButton.disabled = true;

            // 添加AI响应卡片
            const aiMessageElement = addMessageCard('正在思考...', 'ai', true);

            // 调用流式API
            streamResponse(message, aiMessageElement);
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // 模型选择变化时更新状态栏
        modelSelect.addEventListener('change', function() {
            console.log('模型已切换为:', this.value);
        });

        // 页面加载完成时初始化状态
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus('connected', '已连接');
            console.log('DeepSeek Chat Cards 已初始化');
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (currentEventSource) {
                currentEventSource.close();
            }
        });
    </script>
</body>
</html>
