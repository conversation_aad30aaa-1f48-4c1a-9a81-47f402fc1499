<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to bottom, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            min-height: 100vh;
            color: #ffffff;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .app-title {
            font-size: 32px;
            color: #42a5f5;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .app-subtitle {
            color: #b0bec5;
            font-size: 16px;
        }

        .chat-area {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .message-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 12px 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            transition: transform 0.2s, box-shadow 0.2s;
            max-width: 70%;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.4);
            background: rgba(255, 255, 255, 0.98);
        }

        .message-card.user {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            color: white;
            margin-left: auto;
            margin-right: 20px;
            max-width: fit-content;
            min-width: 100px;
            width: auto;
        }

        .message-card.ai {
            background: rgba(255, 255, 255, 0.95);
            margin-right: 30%;
            margin-left: 0;
            border-left: 4px solid #1877f2;
            color: #333;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .user .avatar {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .ai .avatar {
            background: #1877f2;
            color: white;
        }

        .message-info {
            flex: 1;
        }

        .sender-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
        }

        .message-text {
            line-height: 1.6;
            font-size: 15px;
        }

        .input-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            position: sticky;
            bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .input-container {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 50px;
            padding: 15px 20px;
            border: 2px solid #e4e6ea;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #1877f2;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #1877f2;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #166fe5;
        }

        .send-button:disabled {
            background: #bcc0c4;
            cursor: not-allowed;
        }

        .clear-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #f44336;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: background 0.3s;
            margin-right: 10px;
        }

        .clear-button:hover {
            background: #d32f2f;
        }

        .clear-button:disabled {
            background: #bcc0c4;
            cursor: not-allowed;
        }

        .status-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: rgba(40, 167, 69, 0.2);
            color: #4caf50;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .model-select {
            padding: 5px 10px;
            border: 1px solid #4caf50;
            border-radius: 5px;
            background: white;
            color: #2e7d32;
            font-size: 13px;
            outline: none;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .status-dot.disconnected {
            background: #f44336;
        }

        .status-dot.connecting {
            background: #ff9800;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .message-card {
                max-width: 85%;
                padding: 10px 12px;
            }

            .message-card.user {
                margin-right: 15px;
                max-width: fit-content;
            }

            .message-card.ai {
                margin-right: 15%;
            }

            .avatar {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .clear-button, .send-button {
                width: 45px;
                height: 45px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1 class="app-title">🧠 DeepSeek AI Chat</h1>
            <p class="app-subtitle">智能对话助手 - 流式响应体验</p>
        </div>

        <div class="status-bar">
            <div class="status-left">
                <div class="connection-status">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="connectionStatus">已连接</span>
                </div>
                <span>模式：流式对话</span>
            </div>
            <div class="status-right">
                <label for="modelSelect">模型：</label>
                <select class="model-select" id="modelSelect">
                    <option value="deepseek-r1:1.5b">DeepSeek R1 1.5B</option>
                </select>
            </div>
        </div>

        <div class="chat-area" id="chatArea">
            <div class="message-card ai">
                <div class="message-header">
                    <div class="avatar">🤖</div>
                    <div class="message-info">
                        <div class="sender-name">DeepSeek AI</div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
                <div class="message-text">
                    欢迎使用DeepSeek AI助手！我已准备好为您提供帮助。请随时向我提问，我会以流式方式实时回复您。
                </div>
            </div>
        </div>

        <div class="input-card">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea class="message-input" id="messageInput" placeholder="输入您的问题..." rows="1"></textarea>
                </div>
                <button class="clear-button" id="clearButton" title="清除对话">
                    🗑️
                </button>
                <button class="send-button" id="sendButton">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const clearButton = document.getElementById('clearButton');
        const modelSelect = document.getElementById('modelSelect');
        const statusDot = document.getElementById('statusDot');
        const connectionStatus = document.getElementById('connectionStatus');

        let currentEventSource = null;
        let isGenerating = false;

        // 过滤DeepSeek的think标签
        function filterThinkTags(content) {
            if (!content) return content;
            // 移除<think>...</think>标签及其内容（支持多行和嵌套）
            return content.replace(/<think>[\s\S]*?<\/think>/gi, '').trim();
        }

        function addMessageCard(content, type, isStreaming = false) {
            const card = document.createElement('div');
            card.className = `message-card ${type}`;

            const avatar = type === 'user' ? '👤' : '🤖';
            const name = type === 'user' ? '您' : 'DeepSeek AI';
            const time = new Date().toLocaleTimeString();
            const messageId = `message-${Date.now()}`;

            card.innerHTML = `
                <div class="message-header">
                    <div class="avatar">${avatar}</div>
                    <div class="message-info">
                        <div class="sender-name">${name}</div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
                <div class="message-text" id="${messageId}">${content}</div>
            `;

            chatArea.appendChild(card);
            card.scrollIntoView({ behavior: 'smooth' });

            return card.querySelector('.message-text');
        }

        // 流式响应处理函数
        function streamResponse(message, aiMessageElement) {
            const model = modelSelect.value;
            const apiUrl = `http://localhost:8090/api/v1/ollama/generate_stream?model=${encodeURIComponent(model)}&message=${encodeURIComponent(message)}`;

            updateConnectionStatus('connecting', '连接中...');

            currentEventSource = new EventSource(apiUrl);
            let fullContent = '';

            currentEventSource.onopen = function(event) {
                console.log('EventSource连接已建立');
                console.log('API URL:', apiUrl);
                updateConnectionStatus('connected', '已连接');
                // 清除初始的"正在思考..."文本
                aiMessageElement.textContent = '';
            };

            currentEventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);

                    // 处理单个JSON对象格式的响应
                    if (data && data.result) {
                        const result = data.result;
                        const content = result.output.content;
                        const finishReason = result.metadata.finishReason;

                        // 调试信息：记录finishReason状态
                        if (finishReason !== null) {
                            console.log('收到finishReason:', finishReason);
                        }

                        // 如果有内容，追加到当前消息（注意content可能为空）
                        if (content) {
                            // 过滤think标签后再添加内容
                            const filteredContent = filterThinkTags(content);
                            if (filteredContent) {
                                fullContent += filteredContent;
                                // 再次过滤完整内容，确保没有遗漏的think标签
                                const finalContent = filterThinkTags(fullContent);
                                aiMessageElement.textContent = finalContent;
                                // 滚动到最新消息
                                aiMessageElement.scrollIntoView({ behavior: 'smooth' });
                            }
                        }

                        // 检查是否结束 - 支持多种结束状态
                        if (finishReason && finishReason !== null) {
                            console.log('检测到结束标识:', finishReason);
                            finishResponse();
                        }
                    }
                } catch (error) {
                    console.error('解析响应数据失败:', error);
                    console.error('原始数据:', event.data);
                    handleResponseError('数据解析失败: ' + error.message);
                }
            };

            currentEventSource.onerror = function(event) {
                console.error('EventSource连接错误:', event);
                console.error('EventSource readyState:', currentEventSource.readyState);

                // 检查是否是正常结束后的连接关闭
                if (currentEventSource.readyState === EventSource.CLOSED) {
                    console.log('连接已正常关闭');
                    finishResponse();
                } else {
                    console.error('真正的连接错误，readyState:', currentEventSource.readyState);
                    handleResponseError('连接失败，请检查网络或服务状态');
                }
            };
        }

        // 完成响应处理
        function finishResponse() {
            console.log('开始完成响应处理');

            if (currentEventSource) {
                console.log('关闭EventSource连接，当前状态:', currentEventSource.readyState);
                currentEventSource.close();
                currentEventSource = null;
            }

            isGenerating = false;
            sendButton.disabled = false;
            updateConnectionStatus('connected', '已连接');

            console.log('AI响应完成');
        }

        // 处理响应错误
        function handleResponseError(errorMessage) {
            if (currentEventSource) {
                currentEventSource.close();
                currentEventSource = null;
            }

            // 在聊天区域显示错误消息
            const errorCard = addMessageCard(`❌ 错误: ${errorMessage}`, 'ai');
            errorCard.style.color = '#f44336';

            isGenerating = false;
            sendButton.disabled = false;
            updateConnectionStatus('disconnected', '连接失败');

            console.error('响应错误:', errorMessage);
            console.error('请检查：1. 后端服务是否运行在localhost:8090 2. deepseek-r1:1.5b模型是否可用');
        }

        // 更新连接状态
        function updateConnectionStatus(status, text) {
            statusDot.className = `status-dot ${status}`;
            connectionStatus.textContent = text;
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isGenerating) return;

            // 添加用户消息卡片
            addMessageCard(message, 'user');
            messageInput.value = '';

            // 设置生成状态
            isGenerating = true;
            sendButton.disabled = true;
            console.log('开始发送消息:', message);

            // 添加AI响应卡片
            const aiMessageElement = addMessageCard('正在思考...', 'ai', true);

            // 调用流式API
            streamResponse(message, aiMessageElement);
        }

        // 清除对话功能
        function clearChat() {
            // 确认清除操作
            if (confirm('确定要清除所有对话内容吗？')) {
                // 清空聊天区域
                chatArea.innerHTML = '';

                // 重新添加欢迎消息
                const welcomeCard = document.createElement('div');
                welcomeCard.className = 'message-card ai';
                welcomeCard.innerHTML = `
                    <div class="message-header">
                        <div class="avatar">🤖</div>
                        <div class="message-info">
                            <div class="sender-name">DeepSeek AI</div>
                            <div class="message-time">刚刚</div>
                        </div>
                    </div>
                    <div class="message-text">
                        欢迎使用DeepSeek AI助手！我已准备好为您提供帮助。请随时向我提问，我会以流式方式实时回复您。
                    </div>
                `;
                chatArea.appendChild(welcomeCard);

                console.log('对话已清除');
            }
        }

        sendButton.addEventListener('click', sendMessage);
        clearButton.addEventListener('click', clearChat);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // 模型选择变化时更新状态栏
        modelSelect.addEventListener('change', function() {
            console.log('模型已切换为:', this.value);
        });

        // 页面加载完成时初始化状态
        document.addEventListener('DOMContentLoaded', function() {
            updateConnectionStatus('connected', '已连接');
            console.log('DeepSeek Chat Cards 已初始化');
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (currentEventSource) {
                currentEventSource.close();
            }
        });
    </script>
</body>
</html>
