<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek Chat Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }

        .app-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .app-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .app-title {
            font-size: 32px;
            color: #1877f2;
            margin-bottom: 10px;
        }

        .app-subtitle {
            color: #65676b;
            font-size: 16px;
        }

        .chat-area {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .message-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .message-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .message-card.user {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            color: white;
            margin-left: 20%;
        }

        .message-card.ai {
            background: white;
            margin-right: 20%;
            border-left: 4px solid #1877f2;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .user .avatar {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .ai .avatar {
            background: #1877f2;
            color: white;
        }

        .message-info {
            flex: 1;
        }

        .sender-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
        }

        .message-text {
            line-height: 1.6;
            font-size: 15px;
        }

        .input-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: sticky;
            bottom: 20px;
        }

        .input-container {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .message-input {
            width: 100%;
            min-height: 50px;
            padding: 15px 20px;
            border: 2px solid #e4e6ea;
            border-radius: 25px;
            font-size: 16px;
            resize: none;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #1877f2;
        }

        .send-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #1877f2;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #166fe5;
        }

        .send-button:disabled {
            background: #bcc0c4;
            cursor: not-allowed;
        }

        .status-bar {
            text-align: center;
            padding: 10px;
            background: #e8f5e8;
            color: #2e7d32;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="app-header">
            <h1 class="app-title">🧠 DeepSeek AI Chat</h1>
            <p class="app-subtitle">智能对话助手 - 流式响应体验</p>
        </div>

        <div class="status-bar">
            🟢 连接状态：已连接 | 模型：DeepSeek-1.5B | 模式：流式对话
        </div>

        <div class="chat-area" id="chatArea">
            <div class="message-card ai">
                <div class="message-header">
                    <div class="avatar">🤖</div>
                    <div class="message-info">
                        <div class="sender-name">DeepSeek AI</div>
                        <div class="message-time">刚刚</div>
                    </div>
                </div>
                <div class="message-text">
                    欢迎使用DeepSeek AI助手！我已准备好为您提供帮助。请随时向我提问，我会以流式方式实时回复您。
                </div>
            </div>
        </div>

        <div class="input-card">
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea class="message-input" id="messageInput" placeholder="输入您的问题..." rows="1"></textarea>
                </div>
                <button class="send-button" id="sendButton">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <script>
        const chatArea = document.getElementById('chatArea');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        function addMessageCard(content, type, isStreaming = false) {
            const card = document.createElement('div');
            card.className = `message-card ${type}`;
            
            const avatar = type === 'user' ? '👤' : '🤖';
            const name = type === 'user' ? '您' : 'DeepSeek AI';
            const time = new Date().toLocaleTimeString();
            
            card.innerHTML = `
                <div class="message-header">
                    <div class="avatar">${avatar}</div>
                    <div class="message-info">
                        <div class="sender-name">${name}</div>
                        <div class="message-time">${time}</div>
                    </div>
                </div>
                <div class="message-text" id="message-${Date.now()}">${content}</div>
            `;
            
            chatArea.appendChild(card);
            card.scrollIntoView({ behavior: 'smooth' });
            
            return card.querySelector('.message-text');
        }

        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            addMessageCard(message, 'user');
            messageInput.value = '';
            sendButton.disabled = true;

            // 添加AI响应卡片
            const aiMessageElement = addMessageCard('正在思考...', 'ai', true);
            
            // 这里调用您的流式API
            // streamResponse(message, aiMessageElement);
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 自动调整输入框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
    </script>
</body>
</html>
