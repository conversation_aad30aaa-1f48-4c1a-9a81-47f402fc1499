<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能对话 - DeepSeek Chat</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义动画 */
        @keyframes typing {
            0%, 20% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .typing-indicator {
            animation: typing 1.5s infinite;
        }
        
        .dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        
        /* 滚动条样式 */
        .chat-scroll::-webkit-scrollbar {
            width: 6px;
        }
        
        .chat-scroll::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        
        .chat-scroll::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        
        .chat-scroll::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* 消息动画 */
        .message-enter {
            animation: messageSlideIn 0.3s ease-out;
        }
        
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
    <!-- 主容器 -->
    <div class="flex flex-col h-screen max-w-4xl mx-auto bg-white shadow-2xl">
        <!-- 顶部标题栏 -->
        <header class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4 shadow-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">AI智能对话</h1>
                        <p class="text-blue-100 text-sm">基于DeepSeek模型的智能助手</p>
                    </div>
                </div>
                
                <!-- 模型选择器 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm text-blue-100">模型:</label>
                    <select id="modelSelect" class="bg-white text-gray-800 px-3 py-1 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-300">
                        <option value="deepseek-r1:1.5b">DeepSeek R1 1.5B</option>
                        <option value="deepseek-r1:7b">DeepSeek R1 7B</option>
                        <option value="deepseek-r1:14b">DeepSeek R1 14B</option>
                    </select>
                </div>
            </div>
        </header>

        <!-- 聊天消息区域 -->
        <main class="flex-1 overflow-hidden">
            <div id="chatContainer" class="h-full overflow-y-auto chat-scroll p-4 space-y-4">
                <!-- 欢迎消息 -->
                <div class="flex justify-start">
                    <div class="max-w-xs md:max-w-md lg:max-w-lg">
                        <div class="bg-gray-100 text-gray-800 rounded-2xl rounded-bl-md px-4 py-3 shadow-sm">
                            <p class="text-sm">👋 您好！我是基于DeepSeek模型的AI助手，有什么可以帮助您的吗？</p>
                        </div>
                        <div class="text-xs text-gray-500 mt-1 ml-2">
                            <span id="welcomeTime"></span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 输入区域 -->
        <footer class="bg-white border-t border-gray-200 p-4">
            <div class="flex items-end space-x-3">
                <!-- 输入框 -->
                <div class="flex-1 relative">
                    <textarea 
                        id="messageInput" 
                        placeholder="输入您的问题..." 
                        class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-2xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                        rows="1"
                        style="min-height: 44px; max-height: 120px;"
                    ></textarea>
                    
                    <!-- 字符计数 -->
                    <div class="absolute bottom-2 right-12 text-xs text-gray-400">
                        <span id="charCount">0</span>/1000
                    </div>
                </div>
                
                <!-- 发送按钮 -->
                <button 
                    id="sendButton" 
                    class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white p-3 rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
                    title="发送消息 (Ctrl+Enter)"
                >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z"/>
                    </svg>
                </button>
            </div>
            
            <!-- 状态指示器 -->
            <div id="statusIndicator" class="mt-2 text-sm text-gray-500 hidden">
                <div class="flex items-center space-x-2">
                    <div class="typing-indicator">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    </div>
                    <span>AI正在思考<span class="dots"></span></span>
                </div>
            </div>
        </footer>
    </div>

    <script>
        // 全局变量
        let currentEventSource = null;
        let currentMessageElement = null;
        let isGenerating = false;

        // DOM元素
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const modelSelect = document.getElementById('modelSelect');
        const statusIndicator = document.getElementById('statusIndicator');
        const charCount = document.getElementById('charCount');
        const welcomeTime = document.getElementById('welcomeTime');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置欢迎消息时间
            welcomeTime.textContent = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });

            // 绑定事件
            sendButton.addEventListener('click', handleSendMessage);
            messageInput.addEventListener('keydown', handleKeyDown);
            messageInput.addEventListener('input', handleInputChange);
            
            // 自动调整输入框高度
            messageInput.addEventListener('input', autoResizeTextarea);
        });

        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter') {
                if (event.ctrlKey || event.metaKey) {
                    handleSendMessage();
                } else if (!event.shiftKey) {
                    event.preventDefault();
                    handleSendMessage();
                }
            }
        }

        // 处理输入变化
        function handleInputChange() {
            const length = messageInput.value.length;
            charCount.textContent = length;
            
            // 字符限制
            if (length > 1000) {
                messageInput.value = messageInput.value.substring(0, 1000);
                charCount.textContent = 1000;
            }
            
            // 更新发送按钮状态
            updateSendButtonState();
        }

        // 自动调整输入框高度
        function autoResizeTextarea() {
            messageInput.style.height = 'auto';
            messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
        }

        // 更新发送按钮状态
        function updateSendButtonState() {
            const hasText = messageInput.value.trim().length > 0;
            sendButton.disabled = !hasText || isGenerating;
        }

        // 处理发送消息
        function handleSendMessage() {
            const message = messageInput.value.trim();
            const model = modelSelect.value;
            
            if (!message || isGenerating) return;
            
            // 添加用户消息
            addUserMessage(message);
            
            // 清空输入框
            messageInput.value = '';
            charCount.textContent = '0';
            autoResizeTextarea();
            
            // 开始AI回复
            startAIResponse(model, message);
        }
    </script>
</body>
</html>
